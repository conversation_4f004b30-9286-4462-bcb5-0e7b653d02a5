grafana:
  cluster:
# Moved to values-<clusterID>.yaml
#   name: d0
    platform: "openshift"   # OpenShift - https://github.com/grafana/k8s-monitoring-helm/blob/main/examples/openshift-compatible/values.yaml
      
  externalServices:
    prometheus:
      host: https://prometheus-prod-09-prod-au-southeast-0.grafana.net
#     host: https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp
      proxyURL: http://proxy.det.nsw.edu.au:80
#     protocol: "otlphttp"
#     writeEndpoint: ''
      basicAuth:
#       username: "533612"
        username: "768052"
        # password: This has been moved to the application manifest in ArgoCD
    ##
    ## Validated that this config works.
    ##
    # prometheus:
    #   host: https://mimir-rwb-write.obs.nsw.education
    #   writeEndpoint: '/api/v1/push'
    #   extraHeaders: {
    #     "X-Scope-OrgID": "ocp"
    #   }


    loki:
      host: https://logs-prod-004.grafana.net
      proxyURL: http://proxy.det.nsw.edu.au:80
      basicAuth:
        username: "382995"
        # password: This has been moved to the application manifest in ArgoCD
    tempo:
#     host: https://tempo-prod-03-au-southeast-0.grafana.net:443
      host: https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp
      proxyURL: http://proxy.det.nsw.edu.au:80
      protocol: "otlphttp"
#     tenantId: "533612"
      basicAuth:
#       username: "379508"
        username: "533612"
        # password: This has been moved to the application manifest in ArgoCD
    pyroscope:
      host: https://profiles-prod-009.grafana.net
      proxyURL: http://proxy.det.nsw.edu.au:80
      basicAuth:
        username: "533612"
        # password: This has been moved to the application manifest in ArgoCD

  metrics:
    enabled: true
    extraMetricRelabelingRules: |-
      rule {
        source_labels = ["namespace"]
        regex = "^(openshift-.*|kube-.*|.*-ci$|.*-poc$|.*-demo$|.*-test$|.*-dev$)$"
        action = "drop"
      }
      rule {
        regex = "process_pid"
        action = "labeldrop"
      }
    autoDiscover:
      enabled: true
      namespaces: [astp,grafana,idmapp,idmis,idmsec,smern,smoes,smplat]
    alloy:
      metricsTuning:
        useDefaultAllowList: true
    cost: # If enabled the opencost stanza needs updates in the clusterID values files
      enabled: false
    kepler: # https://github.com/grafana/kepler
      enabled: false
    serviceMonitors: 
      enabled: false
    beyla: # https://github.com/grafana/beyla
      enabled: true
    kube-state-metrics: # Alloy scrapes kube-state-metrics from OpenShift
      enabled: true
      metricsTuning:
        useDefaultAllowList: true
      service:
        port: https-main
        isTLS: true
    node-exporter:
      metricsTuning:
        useDefaultAllowList: true
      labelMatchers:
        app.kubernetes.io/name: node-exporter
      service:
        isTLS: true

  logs: 
    enabled: true
    pod_logs:
      enabled: true
      namespaces: [astp,domapi,domstr,grafana,hrapps,idmapp,idmis,idmsec,imsa,lmsapp,smern,smoes,smplat,xfifin]
    cluster_events:
      enabled: true

# This enables alloy to receive data data from applications that have been instrumented
# https://github.com/grafana/k8s-monitoring-helm/tree/v1.6.1/charts/k8s-monitoring-v1/docs/examples/traces-enabled
  traces:
    enabled: true
    receiver:
      filters:
        span:
          - attributes["http.route"] == "/live"
          - attributes["http.route"] == "/healthy"
          - attributes["http.route"] == "/ready"
    transforms: # This transform is a fix for some jvm traces that have too many attributes
      resource:
        - limit(attributes, 100, [])
        - truncate_all(attributes, 4096)
      span:
        - limit(attributes, 100, [])
        - truncate_all(attributes, 4096)

  profiles: # Beyla based Profiling, supports Go C++ Java, is limited to specific namespaces
    enabled: false
    
  receivers:
    grpc:
      enabled: true
    http:
      enabled: true
    zipkin:
      enabled: true
    # Required https://grafana.com/docs/grafana-cloud/monitor-applications/application-observability/setup/instrumentation-quality/#add-missing-billing-telemetry-metric
    grafanaCloudMetrics: # Sends metrics for traces to GrafanaCloud, if disabled then GrafanaCloud has to generate these at extra cost
      enabled: false
    deployGrafanaAgentService: true # this is default with the current version. once smplat have reconfigured prod workloads this can be set to false and will save some compute/memory.
    

# These three features are not needed on OpenShift, alloy is configured under metrics stanza above where to get these metrics
  kube-state-metrics:
    enabled: false # OpenShift already has
  prometheus-node-exporter:
    enabled: false # OpenShift already has
  prometheus-operator-crds:
    enabled: false # OpenShift already has

  beyla: # Generates RED metrics for services, filtered to namespaces, this is a low volume signal
    enabled: true
    image:
      registry: quay.education.nsw.gov.au
      repository: docker.io/grafana/beyla
    config:
      data:
        attributes:
          kubernetes:
            enable: true      
        otel_traces_export:
          sampler:
            name: "always_off"
        routes:
          ignored_patterns:
            - /*
            - /metrics
            - /management
            - /health
            - /health/live
            - /-/ready
            - /*/actuator/health/readiness
            - /*/actuator/health/liveness
            - /actuator/health/readiness
          ignore_mode: trace #ignores metrics for tha above patterns, metrics will as a minimum provide an "alive" state
        discovery:
          exclude_services:
            - exe_path: /usr/bin/conmon
            - exe_path: /usr/bin/coreutils
          services:
            - k8s_namespace: "^(astp|idmis|idmsec|smern|smoes|smplat)$"
              exe_path: /usr/bin/java

  alloy:
    image:
      registry: quay.education.nsw.gov.au
      repository: docker.io/grafana/alloy
    alloy:
      extraEnv: &extraenv
        - name: HTTP_PROXY
          value: http://proxy.det.nsw.edu.au:80
        - name: HTTPS_PROXY 
          value: http://proxy.det.nsw.edu.au:80
        - name: NO_PROXY
          value: "172.30.0.1,.cluster.local"
      securityContext:
        allowPrivilegeEscalation: false
        capabilities:
          drop: [ "ALL" ]
          add: [ "CHOWN", "DAC_OVERRIDE", "FOWNER", "FSETID", "KILL", "SETGID", "SETUID", "SETPCAP", "NET_BIND_SERVICE", "NET_RAW", "SYS_CHROOT", "MKNOD", "AUDIT_WRITE", "SETFCAP" ]
        seccompProfile:
          type: "RuntimeDefault"

  alloy-logs:
    alloy:
      extraEnv: *extraenv
      securityContext:
        allowPrivilegeEscalation: false
        capabilities:
          drop: [ "ALL" ]
          add: [ "CHOWN", "DAC_OVERRIDE", "FOWNER", "FSETID", "KILL", "SETGID", "SETUID", "SETPCAP", "NET_BIND_SERVICE", "NET_RAW", "SYS_CHROOT", "MKNOD", "AUDIT_WRITE", "SETFCAP" ]
        privileged: false
        runAsUser: 0
    global:
      podSecurityContext:
        seLinuxOptions:
          type: spc_t

  alloy-events:
    image:
      registry: quay.education.nsw.gov.au
      repository: docker.io/grafana/alloy
    alloy:
      extraEnv: *extraenv
      securityContext:
        allowPrivilegeEscalation: false
        capabilities:
          drop: [ "ALL" ]
          add: [ "CHOWN", "DAC_OVERRIDE", "FOWNER", "FSETID", "KILL", "SETGID", "SETUID", "SETPCAP", "NET_BIND_SERVICE", "NET_RAW", "SYS_CHROOT", "MKNOD", "AUDIT_WRITE", "SETFCAP" ]
        seccompProfile:
          type: "RuntimeDefault"

  opencost:
    enabled: false
  #   opencost:
  #     exporter:
  #       defaultClusterId: d0
  #       extraEnv:
  #         HTTP_PROXY: http://proxy.det.nsw.edu.au:80
  #         HTTPS_PROXY: http://proxy.det.nsw.edu.au:80
  #         NO_PROXY: "172.30.0.1,.cluster.local"
  #     prometheus:
  #       username: "768052"
  #       # password: @see Sops
  #       external:
  #         url: https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom
